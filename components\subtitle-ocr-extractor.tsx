"use client"

import React, { useState, use<PERSON><PERSON>back, useRef } from "react"
import {
  Upload,
  FileVideo,
  Play,
  Pause,
  Square,
  Settings,
  Download,
  Loader2,
  CheckCircle2,
  AlertCircle,
  ArrowRight,
  Sc<PERSON>,
  <PERSON>,
  FileText,
  Wand2
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Slider } from "@/components/ui/slider"
import { Checkbox } from "@/components/ui/checkbox"
import { useToast } from "@/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogDescription,
  DialogFooter
} from "@/components/ui/dialog"
import { ocrService, type OCRConfig, type SubtitleItem } from "@/utils/ocr-service"

// OCR提取状态
type ExtractionStatus = 'idle' | 'loading' | 'extracting' | 'completed' | 'error'

// 提取结果接口
interface ExtractionResult {
  id: string
  videoName: string
  videoPath: string
  duration: number
  subtitles: SubtitleItem[]
  extractionTime: Date
  totalFrames: number
  processedFrames: number
}

// 默认OCR配置
const defaultOCRConfig: OCRConfig = {
  detectionThreshold: 0.7,
  recognitionThreshold: 0.8,
  mergeThreshold: 0.5,
  frameInterval: 1000, // 每秒提取一帧
  subtitleRegion: {
    enabled: false,
    x: 0,
    y: 0.8,
    width: 1,
    height: 0.2
  },
  deduplication: true,
  minTextLength: 2
}

interface SubtitleOCRExtractorProps {
  onNavigateToGenerator?: (subtitles: SubtitleItem[]) => void
  onOpenGlobalSettings?: (section?: string) => void
}

export function SubtitleOCRExtractor({ 
  onNavigateToGenerator,
  onOpenGlobalSettings 
}: SubtitleOCRExtractorProps) {
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // 状态管理
  const [extractionStatus, setExtractionStatus] = useState<ExtractionStatus>('idle')
  const [extractionProgress, setExtractionProgress] = useState(0)
  const [currentVideo, setCurrentVideo] = useState<File | null>(null)
  const [extractionResult, setExtractionResult] = useState<ExtractionResult | null>(null)
  const [ocrConfig, setOCRConfig] = useState<OCRConfig>(defaultOCRConfig)
  const [showSettings, setShowSettings] = useState(false)
  const [currentFrame, setCurrentFrame] = useState(0)
  const [totalFrames, setTotalFrames] = useState(0)

  // 处理文件上传
  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // 检查文件类型
    if (!file.type.startsWith('video/')) {
      toast({
        title: "文件类型错误",
        description: "请选择视频文件",
        variant: "destructive"
      })
      return
    }

    setCurrentVideo(file)
    setExtractionResult(null)
    setExtractionStatus('idle')
    setExtractionProgress(0)

    toast({
      title: "视频文件已加载",
      description: `已选择文件: ${file.name}`,
    })
  }, [toast])

  // 开始字幕提取
  const handleStartExtraction = useCallback(async () => {
    if (!currentVideo) {
      toast({
        title: "请先选择视频文件",
        variant: "destructive"
      })
      return
    }

    setExtractionStatus('loading')
    setExtractionProgress(0)

    try {
      // 初始化OCR服务
      if (!ocrService.isModelInitialized()) {
        toast({
          title: "正在初始化OCR模型...",
          description: "首次使用需要加载模型，请稍候"
        })
        await ocrService.initialize()
      }

      setExtractionStatus('extracting')

      // 使用OCR服务提取字幕
      const subtitles = await ocrService.extractSubtitlesFromVideo(
        currentVideo,
        ocrConfig,
        (progress, current, total) => {
          setExtractionProgress(progress)
          setCurrentFrame(current)
          setTotalFrames(total)
        }
      )

      // 创建提取结果
      const result: ExtractionResult = {
        id: `extraction-${Date.now()}`,
        videoName: currentVideo.name,
        videoPath: URL.createObjectURL(currentVideo),
        duration: 0, // 将在视频加载后获取
        subtitles,
        extractionTime: new Date(),
        totalFrames: totalFrames,
        processedFrames: currentFrame
      }

      setExtractionResult(result)
      setExtractionStatus('completed')

      toast({
        title: "提取完成",
        description: `成功提取 ${subtitles.length} 条字幕`,
      })

    } catch (error) {
      console.error('字幕提取失败:', error)
      setExtractionStatus('error')
      toast({
        title: "提取失败",
        description: error instanceof Error ? error.message : "字幕提取过程中发生错误",
        variant: "destructive"
      })
    }
  }, [currentVideo, ocrConfig, toast, totalFrames, currentFrame])

  // 跳转到AI生成页面
  const handleNavigateToGenerator = useCallback(() => {
    if (extractionResult && onNavigateToGenerator) {
      onNavigateToGenerator(extractionResult.subtitles)
    }
  }, [extractionResult, onNavigateToGenerator])

  // 导出字幕文件
  const handleExportSubtitles = useCallback(() => {
    if (!extractionResult) return

    // 生成SRT格式字幕
    let srtContent = ''
    extractionResult.subtitles.forEach((subtitle, index) => {
      const startTime = formatTime(subtitle.startTime)
      const endTime = formatTime(subtitle.endTime)

      srtContent += `${index + 1}\n`
      srtContent += `${startTime} --> ${endTime}\n`
      srtContent += `${subtitle.text}\n\n`
    })

    // 创建下载链接
    const blob = new Blob([srtContent], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = `${extractionResult.videoName.replace(/\.[^/.]+$/, '')}_subtitles.srt`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    toast({
      title: "导出成功",
      description: "字幕文件已保存到下载目录",
    })
  }, [extractionResult, toast])

  // 格式化时间为SRT格式
  const formatTime = (milliseconds: number): string => {
    const totalSeconds = Math.floor(milliseconds / 1000)
    const hours = Math.floor(totalSeconds / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60
    const ms = milliseconds % 1000

    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
  }

  // 渲染上传区域
  const renderUploadArea = () => (
    <Card className="border-2 border-dashed border-blue-200 dark:border-blue-800 bg-blue-50/30 dark:bg-blue-950/30">
      <CardContent className="flex flex-col items-center justify-center py-12">
        <div className="relative mb-4">
          <div className="absolute inset-0 bg-blue-500 blur-md opacity-20 rounded-full"></div>
          <div className="relative bg-gradient-to-br from-blue-500 to-indigo-600 p-4 rounded-full text-white">
            <FileVideo className="h-8 w-8" />
          </div>
        </div>
        
        <h3 className="text-lg font-semibold mb-2">上传视频文件</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 text-center">
          支持 MP4、AVI、MOV 等常见视频格式
        </p>
        
        <Button 
          onClick={() => fileInputRef.current?.click()}
          className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
        >
          <Upload className="h-4 w-4 mr-2" />
          选择视频文件
        </Button>
        
        <input
          ref={fileInputRef}
          type="file"
          accept="video/*"
          onChange={handleFileUpload}
          className="hidden"
        />
      </CardContent>
    </Card>
  )

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-blue-50/50 via-indigo-50/30 to-purple-50/50 dark:from-blue-950/20 dark:via-indigo-950/10 dark:to-purple-950/20">
      {/* 头部 */}
      <div className="flex-shrink-0 p-6 border-b border-blue-100/50 dark:border-blue-900/30 bg-white/50 dark:bg-gray-900/50 backdrop-blur-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <div className="absolute inset-0 bg-blue-500 blur-md opacity-20 rounded-full"></div>
              <div className="relative bg-gradient-to-br from-blue-500 to-indigo-600 p-2 rounded-full text-white">
                <Scan className="h-5 w-5" />
              </div>
            </div>
            <div>
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                硬字幕提取
              </h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                使用OCR技术从视频中提取硬字幕文本
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowSettings(true)}
            >
              <Settings className="h-4 w-4 mr-2" />
              设置
            </Button>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <div className="flex-1 p-6 overflow-auto">
        {!currentVideo ? (
          renderUploadArea()
        ) : (
          <div className="space-y-6">
            {/* 视频信息卡片 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <FileVideo className="h-5 w-5" />
                  <span>视频信息</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium">文件名</Label>
                    <p className="text-sm text-gray-600 dark:text-gray-400">{currentVideo.name}</p>
                  </div>
                  <div>
                    <Label className="text-sm font-medium">文件大小</Label>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {(currentVideo.size / 1024 / 1024).toFixed(2)} MB
                    </p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2 mt-4">
                  <Button
                    onClick={handleStartExtraction}
                    disabled={extractionStatus === 'extracting' || extractionStatus === 'loading'}
                    className="bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700"
                  >
                    {extractionStatus === 'extracting' || extractionStatus === 'loading' ? (
                      <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Scan className="h-4 w-4 mr-2" />
                    )}
                    {extractionStatus === 'extracting' ? '提取中...' : 
                     extractionStatus === 'loading' ? '准备中...' : '开始提取'}
                  </Button>
                  
                  <Button
                    variant="outline"
                    onClick={() => {
                      setCurrentVideo(null)
                      setExtractionResult(null)
                      setExtractionStatus('idle')
                    }}
                  >
                    重新选择
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* 提取进度 */}
            {(extractionStatus === 'extracting' || extractionStatus === 'loading') && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Clock className="h-5 w-5" />
                    <span>提取进度</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Progress value={extractionProgress} className="w-full" />
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400">
                      <span>已处理帧数: {currentFrame}/{totalFrames}</span>
                      <span>{extractionProgress}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 提取结果 */}
            {extractionResult && extractionStatus === 'completed' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <CheckCircle2 className="h-5 w-5 text-green-500" />
                      <span>提取结果</span>
                    </div>
                    <Badge variant="secondary">
                      {extractionResult.subtitles.length} 条字幕
                    </Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="text-sm text-gray-600 dark:text-gray-400">
                        提取时间: {extractionResult.extractionTime.toLocaleString()}
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={handleExportSubtitles}
                        >
                          <Download className="h-4 w-4 mr-2" />
                          导出字幕
                        </Button>
                        <Button
                          size="sm"
                          onClick={handleNavigateToGenerator}
                          className="bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700"
                        >
                          <Wand2 className="h-4 w-4 mr-2" />
                          生成简介
                          <ArrowRight className="h-4 w-4 ml-2" />
                        </Button>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <ScrollArea className="h-64">
                      <div className="space-y-2">
                        {extractionResult.subtitles.map((subtitle, index) => (
                          <div
                            key={subtitle.id}
                            className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
                          >
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium">#{index + 1}</span>
                              <Badge variant="outline" className="text-xs">
                                置信度: {(subtitle.confidence * 100).toFixed(1)}%
                              </Badge>
                            </div>
                            <p className="text-sm mb-2">{subtitle.text}</p>
                            <div className="text-xs text-gray-500">
                              {Math.floor(subtitle.startTime / 1000)}s - {Math.floor(subtitle.endTime / 1000)}s
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>

      {/* OCR设置对话框 */}
      <Dialog open={showSettings} onOpenChange={setShowSettings}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center space-x-2">
              <Settings className="h-5 w-5" />
              <span>OCR设置</span>
            </DialogTitle>
            <DialogDescription>
              配置OCR识别参数以获得最佳字幕提取效果
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* 检测设置 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">检测设置</h3>

              <div className="space-y-2">
                <Label>检测阈值: {ocrConfig.detectionThreshold}</Label>
                <Slider
                  value={[ocrConfig.detectionThreshold]}
                  onValueChange={([value]) =>
                    setOCRConfig(prev => ({ ...prev, detectionThreshold: value }))
                  }
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  较高的值会减少误检，但可能遗漏一些文本
                </p>
              </div>

              <div className="space-y-2">
                <Label>识别阈值: {ocrConfig.recognitionThreshold}</Label>
                <Slider
                  value={[ocrConfig.recognitionThreshold]}
                  onValueChange={([value]) =>
                    setOCRConfig(prev => ({ ...prev, recognitionThreshold: value }))
                  }
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  较高的值会提高识别准确性，但可能遗漏一些低质量文本
                </p>
              </div>
            </div>

            <Separator />

            {/* 提取设置 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">提取设置</h3>

              <div className="space-y-2">
                <Label>帧间隔 (毫秒): {ocrConfig.frameInterval}</Label>
                <Slider
                  value={[ocrConfig.frameInterval]}
                  onValueChange={([value]) =>
                    setOCRConfig(prev => ({ ...prev, frameInterval: value }))
                  }
                  min={500}
                  max={5000}
                  step={100}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  较小的值会提取更多帧，但处理时间更长
                </p>
              </div>

              <div className="space-y-2">
                <Label>最小文本长度: {ocrConfig.minTextLength}</Label>
                <Slider
                  value={[ocrConfig.minTextLength]}
                  onValueChange={([value]) =>
                    setOCRConfig(prev => ({ ...prev, minTextLength: value }))
                  }
                  min={1}
                  max={10}
                  step={1}
                  className="w-full"
                />
                <p className="text-xs text-gray-500">
                  过滤掉长度小于此值的文本
                </p>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="deduplication"
                  checked={ocrConfig.deduplication}
                  onCheckedChange={(checked) =>
                    setOCRConfig(prev => ({ ...prev, deduplication: !!checked }))
                  }
                />
                <Label htmlFor="deduplication">启用去重</Label>
                <p className="text-xs text-gray-500">
                  自动合并相似的字幕条目
                </p>
              </div>

              {ocrConfig.deduplication && (
                <div className="space-y-2 ml-6">
                  <Label>合并阈值: {ocrConfig.mergeThreshold}</Label>
                  <Slider
                    value={[ocrConfig.mergeThreshold]}
                    onValueChange={([value]) =>
                      setOCRConfig(prev => ({ ...prev, mergeThreshold: value }))
                    }
                    min={0.1}
                    max={1.0}
                    step={0.1}
                    className="w-full"
                  />
                  <p className="text-xs text-gray-500">
                    文本相似度超过此值时会被合并
                  </p>
                </div>
              )}
            </div>

            <Separator />

            {/* 字幕区域设置 */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">字幕区域</h3>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="subtitleRegion"
                  checked={ocrConfig.subtitleRegion.enabled}
                  onCheckedChange={(checked) =>
                    setOCRConfig(prev => ({
                      ...prev,
                      subtitleRegion: { ...prev.subtitleRegion, enabled: !!checked }
                    }))
                  }
                />
                <Label htmlFor="subtitleRegion">限制字幕区域</Label>
                <p className="text-xs text-gray-500">
                  只在指定区域内识别字幕，提高效率
                </p>
              </div>

              {ocrConfig.subtitleRegion.enabled && (
                <div className="space-y-4 ml-6 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>X位置: {ocrConfig.subtitleRegion.x}</Label>
                      <Slider
                        value={[ocrConfig.subtitleRegion.x]}
                        onValueChange={([value]) =>
                          setOCRConfig(prev => ({
                            ...prev,
                            subtitleRegion: { ...prev.subtitleRegion, x: value }
                          }))
                        }
                        min={0}
                        max={1}
                        step={0.01}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Y位置: {ocrConfig.subtitleRegion.y}</Label>
                      <Slider
                        value={[ocrConfig.subtitleRegion.y]}
                        onValueChange={([value]) =>
                          setOCRConfig(prev => ({
                            ...prev,
                            subtitleRegion: { ...prev.subtitleRegion, y: value }
                          }))
                        }
                        min={0}
                        max={1}
                        step={0.01}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>宽度: {ocrConfig.subtitleRegion.width}</Label>
                      <Slider
                        value={[ocrConfig.subtitleRegion.width]}
                        onValueChange={([value]) =>
                          setOCRConfig(prev => ({
                            ...prev,
                            subtitleRegion: { ...prev.subtitleRegion, width: value }
                          }))
                        }
                        min={0.1}
                        max={1}
                        step={0.01}
                        className="w-full"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>高度: {ocrConfig.subtitleRegion.height}</Label>
                      <Slider
                        value={[ocrConfig.subtitleRegion.height]}
                        onValueChange={([value]) =>
                          setOCRConfig(prev => ({
                            ...prev,
                            subtitleRegion: { ...prev.subtitleRegion, height: value }
                          }))
                        }
                        min={0.1}
                        max={1}
                        step={0.01}
                        className="w-full"
                      />
                    </div>
                  </div>
                  <p className="text-xs text-gray-500">
                    所有值为相对于视频尺寸的比例 (0-1)
                  </p>
                </div>
              )}
            </div>
          </div>

          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setOCRConfig(defaultOCRConfig)}
            >
              重置默认
            </Button>
            <Button onClick={() => setShowSettings(false)}>
              确定
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}
