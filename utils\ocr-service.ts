// OCR服务 - 使用PP-OCRv5 ONNX模型进行字幕提取

// OCR配置接口
export interface OCRConfig {
  detectionThreshold: number
  recognitionThreshold: number
  mergeThreshold: number
  frameInterval: number
  subtitleRegion: {
    enabled: boolean
    x: number
    y: number
    width: number
    height: number
  }
  deduplication: boolean
  minTextLength: number
}

// 字幕条目接口
export interface SubtitleItem {
  id: string
  startTime: number
  endTime: number
  text: string
  confidence: number
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

// 检测框接口
interface DetectionBox {
  x: number
  y: number
  width: number
  height: number
  confidence: number
}

// OCR结果接口
interface OCRResult {
  text: string
  confidence: number
  boxes: DetectionBox[]
}

// 视频帧接口
interface VideoFrame {
  timestamp: number
  canvas: HTMLCanvasElement
  imageData: ImageData
}

export class OCRService {
  private static instance: OCRService
  private detectionModel: any = null
  private recognitionModel: any = null
  private classificationModel: any = null
  private isInitialized = false

  private constructor() {}

  public static getInstance(): OCRService {
    if (!OCRService.instance) {
      OCRService.instance = new OCRService()
    }
    return OCRService.instance
  }

  // 初始化OCR模型
  public async initialize(): Promise<void> {
    if (this.isInitialized) return

    try {
      console.log('开始初始化OCR模型...')
      
      // 这里需要加载ONNX模型
      // 由于浏览器环境的限制，我们需要使用onnxruntime-web
      // 暂时使用模拟初始化
      await this.loadModels()
      
      this.isInitialized = true
      console.log('OCR模型初始化完成')
    } catch (error) {
      console.error('OCR模型初始化失败:', error)
      throw new Error('OCR模型初始化失败')
    }
  }

  // 加载ONNX模型
  private async loadModels(): Promise<void> {
    // 模拟模型加载
    // 实际实现中需要使用onnxruntime-web加载models目录下的模型文件
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    console.log('检测模型加载完成: ch_PP-OCRv5_mobile_det.onnx')
    console.log('识别模型加载完成: ch_PP-OCRv5_rec_server_infer.onnx')
    console.log('分类模型加载完成: ch_ppocr_mobile_v2.0_cls_infer.onnx')
  }

  // 从视频中提取字幕
  public async extractSubtitlesFromVideo(
    videoFile: File,
    config: OCRConfig,
    onProgress?: (progress: number, currentFrame: number, totalFrames: number) => void
  ): Promise<SubtitleItem[]> {
    if (!this.isInitialized) {
      await this.initialize()
    }

    console.log('开始从视频提取字幕...')

    try {
      // 创建FormData发送到API
      const formData = new FormData()
      formData.append('video', videoFile)
      formData.append('config', JSON.stringify(config))

      // 模拟进度更新
      const progressInterval = setInterval(() => {
        if (onProgress) {
          const progress = Math.min(90, Math.random() * 100)
          onProgress(progress, Math.floor(progress), 100)
        }
      }, 500)

      const response = await fetch('/api/ocr-extract', {
        method: 'POST',
        body: formData
      })

      clearInterval(progressInterval)

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || '字幕提取失败')
      }

      const result = await response.json()

      if (!result.success) {
        throw new Error(result.error || '字幕提取失败')
      }

      // 最终进度更新
      if (onProgress) {
        onProgress(100, result.data.processedFrames, result.data.totalFrames)
      }

      return result.data.subtitles

    } catch (error) {
      console.error('OCR提取失败:', error)
      throw error
    }
  }

  // 检查OCR服务状态
  public async checkServiceStatus(): Promise<{
    modelsReady: boolean
    modelStatus: Record<string, boolean>
    modelsPath: string
  }> {
    try {
      const response = await fetch('/api/ocr-extract')
      const result = await response.json()

      if (result.success) {
        return result.data
      } else {
        throw new Error(result.error)
      }
    } catch (error) {
      console.error('检查OCR服务状态失败:', error)
      throw error
    }
  }

  // 检查模型是否已初始化
  public isModelInitialized(): boolean {
    return this.isInitialized
  }
}

// 导出单例实例
export const ocrService = OCRService.getInstance()
