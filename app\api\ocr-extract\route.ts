import { NextRequest, NextResponse } from 'next/server'
import path from 'path'
import fs from 'fs'

// OCR配置接口
interface OCRConfig {
  detectionThreshold: number
  recognitionThreshold: number
  mergeThreshold: number
  frameInterval: number
  subtitleRegion: {
    enabled: boolean
    x: number
    y: number
    width: number
    height: number
  }
  deduplication: boolean
  minTextLength: number
}

// 字幕条目接口
interface SubtitleItem {
  id: string
  startTime: number
  endTime: number
  text: string
  confidence: number
  position: {
    x: number
    y: number
    width: number
    height: number
  }
}

/**
 * POST /api/ocr-extract - 从视频中提取硬字幕
 */
export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const videoFile = formData.get('video') as File
    const configStr = formData.get('config') as string
    
    if (!videoFile) {
      return NextResponse.json({
        success: false,
        error: '缺少视频文件'
      }, { status: 400 })
    }

    const config: OCRConfig = configStr ? JSON.parse(configStr) : getDefaultConfig()
    
    console.log(`[OCR Extract] 开始处理视频: ${videoFile.name}`)
    console.log(`[OCR Extract] 配置:`, config)

    // 检查模型文件是否存在
    const modelsPath = path.join(process.cwd(), 'models')
    const detModelPath = path.join(modelsPath, 'det_onnx', 'ch_PP-OCRv5_mobile_det.onnx')
    const recModelPath = path.join(modelsPath, 'rec_onnx', 'ch_PP-OCRv5_rec_server_infer.onnx')
    const clsModelPath = path.join(modelsPath, 'cls_onnx', 'ch_ppocr_mobile_v2.0_cls_infer.onnx')

    if (!fs.existsSync(detModelPath) || !fs.existsSync(recModelPath) || !fs.existsSync(clsModelPath)) {
      return NextResponse.json({
        success: false,
        error: 'OCR模型文件不存在，请确保模型已正确安装'
      }, { status: 500 })
    }

    // 暂时返回模拟数据，实际实现需要集成PP-OCRv5
    const mockSubtitles = await simulateOCRExtraction(videoFile, config)

    return NextResponse.json({
      success: true,
      data: {
        subtitles: mockSubtitles,
        videoName: videoFile.name,
        extractionTime: new Date().toISOString(),
        totalFrames: 100,
        processedFrames: 100
      }
    })

  } catch (error) {
    console.error('[OCR Extract] 处理失败:', error)
    return NextResponse.json({
      success: false,
      error: '字幕提取失败'
    }, { status: 500 })
  }
}

/**
 * GET /api/ocr-extract - 检查OCR服务状态
 */
export async function GET() {
  try {
    // 检查模型文件状态
    const modelsPath = path.join(process.cwd(), 'models')
    const detModelPath = path.join(modelsPath, 'det_onnx', 'ch_PP-OCRv5_mobile_det.onnx')
    const recModelPath = path.join(modelsPath, 'rec_onnx', 'ch_PP-OCRv5_rec_server_infer.onnx')
    const clsModelPath = path.join(modelsPath, 'cls_onnx', 'ch_ppocr_mobile_v2.0_cls_infer.onnx')

    const modelStatus = {
      detection: fs.existsSync(detModelPath),
      recognition: fs.existsSync(recModelPath),
      classification: fs.existsSync(clsModelPath)
    }

    const allModelsReady = Object.values(modelStatus).every(status => status)

    return NextResponse.json({
      success: true,
      data: {
        modelsReady: allModelsReady,
        modelStatus,
        modelsPath
      }
    })

  } catch (error) {
    console.error('[OCR Extract] 状态检查失败:', error)
    return NextResponse.json({
      success: false,
      error: '无法检查OCR服务状态'
    }, { status: 500 })
  }
}

// 获取默认配置
function getDefaultConfig(): OCRConfig {
  return {
    detectionThreshold: 0.7,
    recognitionThreshold: 0.8,
    mergeThreshold: 0.5,
    frameInterval: 1000,
    subtitleRegion: {
      enabled: false,
      x: 0,
      y: 0.8,
      width: 1,
      height: 0.2
    },
    deduplication: true,
    minTextLength: 2
  }
}

// 模拟OCR提取过程
async function simulateOCRExtraction(videoFile: File, config: OCRConfig): Promise<SubtitleItem[]> {
  // 模拟处理时间
  await new Promise(resolve => setTimeout(resolve, 2000))

  // 生成模拟字幕数据
  const mockTexts = [
    '欢迎观看本期节目',
    '今天我们将为大家介绍',
    '这是一个非常有趣的话题',
    '让我们一起来探索',
    '感谢大家的观看',
    '我们下期再见',
    '请关注我们的频道',
    '更多精彩内容等着您'
  ]

  const subtitles: SubtitleItem[] = []
  const duration = 60000 // 假设60秒视频

  for (let i = 0; i < mockTexts.length; i++) {
    const startTime = (i * duration) / mockTexts.length
    const endTime = ((i + 1) * duration) / mockTexts.length

    subtitles.push({
      id: `subtitle-${i + 1}`,
      startTime: Math.floor(startTime),
      endTime: Math.floor(endTime),
      text: mockTexts[i],
      confidence: 0.85 + Math.random() * 0.15, // 0.85-1.0
      position: {
        x: 100 + Math.random() * 200,
        y: 400 + Math.random() * 100,
        width: 200 + Math.random() * 100,
        height: 40 + Math.random() * 20
      }
    })
  }

  // 如果启用去重，进行简单的去重处理
  if (config.deduplication) {
    return deduplicateSubtitles(subtitles, config.mergeThreshold)
  }

  return subtitles
}

// 简单的去重函数
function deduplicateSubtitles(subtitles: SubtitleItem[], threshold: number): SubtitleItem[] {
  if (subtitles.length === 0) return []

  const result: SubtitleItem[] = []
  let current = subtitles[0]

  for (let i = 1; i < subtitles.length; i++) {
    const next = subtitles[i]
    const similarity = calculateTextSimilarity(current.text, next.text)

    if (similarity >= threshold && (next.startTime - current.endTime) < 5000) {
      // 合并相似字幕
      current = {
        ...current,
        endTime: next.endTime,
        confidence: Math.max(current.confidence, next.confidence)
      }
    } else {
      result.push(current)
      current = next
    }
  }

  result.push(current)
  return result
}

// 计算文本相似度
function calculateTextSimilarity(text1: string, text2: string): number {
  if (text1 === text2) return 1.0
  if (!text1 || !text2) return 0.0

  const maxLength = Math.max(text1.length, text2.length)
  const distance = levenshteinDistance(text1, text2)
  return 1 - (distance / maxLength)
}

// 计算编辑距离
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = []

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i]
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1]
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        )
      }
    }
  }

  return matrix[str2.length][str1.length]
}
